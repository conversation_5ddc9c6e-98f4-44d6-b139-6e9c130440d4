<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Countdown Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
        button {
            background: #0f766e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0d5b54;
        }
        .countdown-display {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>SG Bus Buddy - Countdown Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <ol>
            <li>Open the SG Bus Buddy app at <a href="http://localhost:5173" target="_blank">http://localhost:5173</a></li>
            <li>Enter any postal code (e.g., "123456") to see bus stops</li>
            <li>Click the play button (▶) next to any bus arrival time</li>
            <li><strong>NEW:</strong> Watch for the spinning loader on the button while starting countdown</li>
            <li>You should see a green success message with "Countdown Started!"</li>
            <li>Click the "View" button or navigate to the Countdown tab</li>
            <li><strong>NEW:</strong> Notice the loading screen with skeleton placeholders</li>
            <li><strong>NEW:</strong> Watch the countdown initialize with "Loading Countdown..." and spinning icon</li>
            <li>After ~500ms, you should see the actual countdown timer (no "Time's up!" flash!)</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li><span class="success">✓</span> Real bus arrival data from arrivelah2.busrouter.sg API</li>
            <li><span class="success">✓</span> Countdown calculates departure time (arrival - walking time - buffer)</li>
            <li><span class="success">✓</span> Timer updates every second</li>
            <li><span class="success">✓</span> Color changes: Blue → Yellow (5min) → Red (1min) → Gray (missed)</li>
            <li><span class="success">✓</span> Success message with navigation to countdown page</li>
            <li><span class="success">✓</span> Persistent countdown sessions (in memory)</li>
            <li><span class="success">✓</span> <strong>NEW:</strong> Proper loading screens with spinners</li>
            <li><span class="success">✓</span> <strong>NEW:</strong> No more "Time's up!" flash on initialization</li>
            <li><span class="success">✓</span> <strong>NEW:</strong> Loading states for countdown buttons</li>
            <li><span class="success">✓</span> <strong>NEW:</strong> Skeleton loading for countdown page</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test Countdown Calculation</h2>
        <p>Example calculation for Bus 114 arriving in 8 minutes:</p>
        <ul>
            <li>Bus arrival time: <span class="info">Now + 8 minutes</span></li>
            <li>Walking time: <span class="info">3 minutes</span> (from bus stop data)</li>
            <li>Buffer time: <span class="info">3 minutes</span> (user setting)</li>
            <li>Departure time: <span class="info">Now + 8 - 3 - 3 = Now + 2 minutes</span></li>
        </ul>
        <p>So the countdown should show approximately 2 minutes until departure.</p>
    </div>

    <div class="test-section">
        <h2>API Response Example</h2>
        <p>The app fetches real data from: <code>https://arrivelah2.busrouter.sg/?id=66609</code></p>
        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">
{
  "services": [
    {
      "no": "114",
      "operator": "SBST",
      "next": {
        "time": "2025-09-03T13:05:39+08:00",
        "duration_ms": 478227,
        "load": "SEA",
        "feature": "WAB",
        "type": "DD"
      }
    }
  ]
}
        </pre>
    </div>

    <div class="test-section">
        <h2>Quick Test</h2>
        <button onclick="testAPI()">Test API Connection</button>
        <button onclick="testTimeCalculation()">Test Time Calculation</button>
        <div id="test-results"></div>
    </div>

    <script>
        async function testAPI() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<p class="info">Testing API connection...</p>';
            
            try {
                const response = await fetch('https://arrivelah2.busrouter.sg/?id=66609');
                const data = await response.json();
                
                if (data.services && data.services.length > 0) {
                    results.innerHTML = `
                        <p class="success">✓ API connection successful!</p>
                        <p>Found ${data.services.length} bus services</p>
                        <p>First service: Bus ${data.services[0].no} (${data.services[0].operator})</p>
                    `;
                } else {
                    results.innerHTML = '<p class="error">✗ API returned no services</p>';
                }
            } catch (error) {
                results.innerHTML = `<p class="error">✗ API test failed: ${error.message}</p>`;
            }
        }

        function testTimeCalculation() {
            const results = document.getElementById('test-results');
            
            // Simulate arrival in 8 minutes
            const arrivalTime = new Date(Date.now() + 8 * 60000);
            const walkingTime = 3; // minutes
            const bufferTime = 3; // minutes
            const departureTime = new Date(arrivalTime.getTime() - (walkingTime + bufferTime) * 60000);
            
            const timeUntilDeparture = Math.max(0, Math.floor((departureTime.getTime() - Date.now()) / 1000));
            const minutes = Math.floor(timeUntilDeparture / 60);
            const seconds = timeUntilDeparture % 60;
            
            results.innerHTML = `
                <p class="success">✓ Time calculation test</p>
                <p>Bus arrives at: ${arrivalTime.toLocaleTimeString()}</p>
                <p>Should leave by: ${departureTime.toLocaleTimeString()}</p>
                <p>Time remaining: ${minutes}:${seconds.toString().padStart(2, '0')}</p>
                <div class="countdown-display">${minutes}:${seconds.toString().padStart(2, '0')}</div>
            `;
        }
    </script>
</body>
</html>
