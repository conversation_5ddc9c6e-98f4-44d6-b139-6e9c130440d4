import React, { useState, useEffect } from "react";
import { BusStop } from "../Entities/BusStop";
import { CountdownSession } from "../Entities/CountdownSession";
import PostalCodeInput from "../Components/PostalCodeInput";
import BusStopCard from "../Components/BusStopCard";
import { Alert, AlertDescription } from "../components/ui/alert";
import { Button } from "../components/ui/button";
import { AlertCircle, Bus, CheckCircle, Clock } from "lucide-react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

export default function HomePage() {
  const [busStops, setBusStops] = useState([]);
  const [activeSessions, setActiveSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [userSettings, setUserSettings] = useState({ arrival_buffer_minutes: 3 });
  const [hasSearched, setHasSearched] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const stops = await BusStop.list("-created_date");
      setBusStops(stops);
      
      const sessions = await CountdownSession.filter({ is_active: true }, "-created_date");
      setActiveSessions(sessions);
    } catch (error) {
      console.error("Could not load user data:", error);
    }
  };

  const findBusStops = async (postalCode) => {
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null); // Clear any success messages
    setHasSearched(true);

    try {
      // Use API to find bus stops near postal code
      const foundStops = await BusStop.findByPostalCode(postalCode);
      setBusStops(foundStops);

      // Refresh active sessions to get latest state
      const sessions = await CountdownSession.filter({ is_active: true }, "-created_date");
      setActiveSessions(sessions);
    } catch (err) {
      console.error("Error finding bus stops:", err);
      setError("Unable to find bus stops for this postal code. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const startCountdown = async (busStop, arrivalInfo) => {
    try {
      // Clear any previous error messages
      setError(null);

      // Parse the arrival time from the API response
      let arrivalMinutes;
      if (arrivalInfo.arrivalTime === "Arriving") {
        arrivalMinutes = 1; // Assume 1 minute if arriving now
      } else {
        // Extract minutes from "X min" format
        const match = arrivalInfo.arrivalTime.match(/(\d+)\s*min/);
        arrivalMinutes = match ? parseInt(match[1]) : 5; // Default to 5 minutes if parsing fails
      }

      // Calculate actual bus arrival time
      const busArrivalTime = new Date(Date.now() + arrivalMinutes * 60000);

      // Calculate when user should leave (arrival time minus walking time and buffer)
      const totalBufferMinutes = busStop.walking_time_minutes + userSettings.arrival_buffer_minutes;
      const departureTime = new Date(busArrivalTime.getTime() - totalBufferMinutes * 60000);

      const session = await CountdownSession.create({
        bus_stop: busStop,
        bus_service: arrivalInfo.service,
        bus_arrival_time: busArrivalTime,
        target_departure_time: departureTime,
        walking_buffer_minutes: userSettings.arrival_buffer_minutes
      });

      setActiveSessions(prev => [...prev, session]);

      // Show success message
      setSuccessMessage({
        busService: arrivalInfo.service,
        busStop: busStop.stop_name,
        departureTime: departureTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      });

      // Clear success message after 5 seconds
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (error) {
      console.error("Error starting countdown:", error);
      setError("Failed to start countdown. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-blue-50">
      <div className="max-w-md mx-auto p-4 pb-24">
        {!hasSearched && (
          <PostalCodeInput 
            onSubmit={findBusStops} 
            isLoading={isLoading}
          />
        )}

        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {successMessage && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-6"
          >
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Countdown Started!</p>
                    <p className="text-sm">Bus {successMessage.busService} at {successMessage.busStop}</p>
                    <p className="text-xs">Leave by {successMessage.departureTime}</p>
                  </div>
                  <Link to="/countdown">
                    <Button size="sm" className="btn-primary text-white">
                      <Clock className="w-4 h-4 mr-1" />
                      View
                    </Button>
                  </Link>
                </div>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {hasSearched && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-slate-800 mb-2">Nearby Bus Stops</h2>
              <p className="text-slate-600">Tap the play button to start a countdown</p>
            </div>

            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-48 bg-white/50 rounded-xl animate-pulse" />
                ))}
              </div>
            ) : busStops.length > 0 ? (
              <motion.div 
                className="space-y-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {busStops.map((busStop, index) => (
                  <motion.div
                    key={busStop.id || index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <BusStopCard
                      busStop={busStop}
                      onStartCountdown={startCountdown}
                      isActive={activeSessions.some(s => s.bus_stop?.id === busStop.id)}
                    />
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <div className="text-center py-12">
                <Bus className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">No bus stops found for this postal code</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
