import React, { useState, useEffect } from "react";
import { CountdownSession } from "../Entities/CountdownSession";
import CountdownTimer from "../Components/CountdownTimer";
import { Alert, AlertDescription } from "../components/ui/alert";
import { Button } from "../components/ui/button";
import { Clock, Plus, AlertCircle } from "lucide-react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

export default function CountdownPage() {
  const [activeSessions, setActiveSessions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadActiveSessions();
  }, []);

  const loadActiveSessions = async () => {
    try {
      const sessions = await CountdownSession.filter({ is_active: true }, "-created_date");
      setActiveSessions(sessions);
    } catch (error) {
      console.error("Error loading sessions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const stopCountdown = async (session) => {
    try {
      await session.delete();
      setActiveSessions(prev => prev.filter(s => s.id !== session.id));
    } catch (error) {
      console.error("Error stopping countdown:", error);
    }
  };

  const restartCountdown = async (session) => {
    try {
      // Restart with new target time - assume next bus in 15 minutes
      const busArrivalTime = new Date(Date.now() + 15 * 60000);
      const totalBufferMinutes = session.bus_stop.walking_time_minutes + session.walking_buffer_minutes;
      const departureTime = new Date(busArrivalTime.getTime() - totalBufferMinutes * 60000);

      const newSession = await CountdownSession.create({
        bus_stop: session.bus_stop,
        bus_service: session.bus_service,
        bus_arrival_time: busArrivalTime,
        target_departure_time: departureTime,
        walking_buffer_minutes: session.walking_buffer_minutes
      });

      await session.delete();
      setActiveSessions(prev =>
        prev.map(s => s.id === session.id ? newSession : s)
      );
    } catch (error) {
      console.error("Error restarting countdown:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-blue-50 p-4">
        <div className="max-w-md mx-auto pb-24">
          <div className="text-center mb-6 pt-4">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center shadow-lg">
              <Clock className="w-8 h-8 text-white animate-pulse" />
            </div>
            <h1 className="text-2xl font-bold text-slate-800 mb-2">Loading Countdowns</h1>
            <p className="text-slate-600">Fetching your active timers...</p>
          </div>

          <div className="space-y-4">
            {[1, 2].map(i => (
              <div key={i} className="glass-effect border-0 shadow-xl overflow-hidden rounded-xl">
                <div className="bg-gradient-to-br from-slate-400 to-slate-500 p-8 text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-white/20 rounded-full animate-pulse"></div>
                  <div className="h-6 bg-white/20 rounded mb-2 animate-pulse"></div>
                  <div className="h-12 bg-white/20 rounded mb-4 animate-pulse"></div>
                  <div className="h-4 bg-white/20 rounded animate-pulse"></div>
                </div>
                <div className="p-6 space-y-4">
                  <div className="h-4 bg-slate-200 rounded animate-pulse"></div>
                  <div className="h-3 bg-slate-200 rounded animate-pulse w-3/4"></div>
                  <div className="flex gap-3">
                    <div className="flex-1 h-10 bg-slate-200 rounded-xl animate-pulse"></div>
                    <div className="flex-1 h-10 bg-slate-200 rounded-xl animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-blue-50 p-4">
      <div className="max-w-md mx-auto pb-24">
        <div className="text-center mb-6 pt-4">
          <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center shadow-lg">
            <Clock className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-slate-800 mb-2">Active Countdowns</h1>
          <p className="text-slate-600">Track your bus departure times</p>
        </div>

        {activeSessions.length > 0 ? (
          <motion.div 
            className="space-y-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {activeSessions.map((session, index) => (
              <motion.div
                key={session.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <CountdownTimer
                  session={session}
                  onStop={() => stopCountdown(session)}
                  onRestart={() => restartCountdown(session)}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <div className="text-center py-12">
            <div className="glass-effect rounded-2xl p-8 border-0 shadow-xl">
              <AlertCircle className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-slate-700 mb-2">No Active Countdowns</h3>
              <p className="text-slate-600 mb-6">Start tracking your bus arrival times from the home screen</p>
              
              <Link to="/home">
                <Button className="btn-primary text-white shadow-lg">
                  <Plus className="w-4 h-4 mr-2" />
                  Find Bus Stops
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
