import React, { useState, useEffect } from "react";
import { Card, CardContent } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { AlertTriangle, Clock, MapPin, StopCircle, RefreshCw, Loader2 } from "lucide-react";
import { motion } from "framer-motion";

export default function CountdownTimer({ session, onStop, onRestart }) {
  const [timeLeft, setTimeLeft] = useState(null); // Start with null to indicate loading
  const [status, setStatus] = useState('loading'); // loading, waiting, warning, critical, missed
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Validate session data first
    if (!session.target_departure_time) {
      setStatus('error');
      setIsInitialized(true);
      return;
    }

    // Initial calculation to set loading state
    const calculateTime = () => {
      const now = new Date().getTime();
      const departureTime = new Date(session.target_departure_time).getTime();
      const remaining = Math.floor((departureTime - now) / 1000);

      setTimeLeft(remaining);

      if (remaining <= 0) {
        setStatus('missed');
      } else if (remaining <= 60) {
        setStatus('critical');
      } else if (remaining <= 300) {
        setStatus('warning');
      } else {
        setStatus('waiting');
      }

      // Mark as initialized after first calculation
      if (!isInitialized) {
        setIsInitialized(true);
      }
    };

    // Add a small delay to show loading state and prevent flash
    let intervalTimer;

    const initTimer = setTimeout(() => {
      calculateTime();

      // Then set up interval for updates
      intervalTimer = setInterval(calculateTime, 1000);
    }, 500); // 500ms delay to show loading state

    return () => {
      clearTimeout(initTimer);
      if (intervalTimer) {
        clearInterval(intervalTimer);
      }
    };
  }, [session.target_departure_time, isInitialized]);

  const formatTime = (seconds) => {
    if (seconds === null || !isInitialized) return "--:--";
    if (seconds <= 0) return "Time's up!";
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusConfig = () => {
    switch (status) {
      case 'loading':
        return {
          bgColor: 'bg-gradient-to-br from-slate-400 to-slate-500',
          textColor: 'text-white',
          message: 'Loading Countdown...',
          icon: Loader2,
          pulse: false,
          spin: true
        };
      case 'error':
        return {
          bgColor: 'bg-gradient-to-br from-red-400 to-red-500',
          textColor: 'text-white',
          message: 'Error Loading',
          icon: AlertTriangle,
          pulse: false
        };
      case 'critical':
        return {
          bgColor: 'bg-gradient-to-br from-red-500 to-pink-500',
          textColor: 'text-white',
          message: 'LEAVE NOW!',
          icon: AlertTriangle,
          pulse: true
        };
      case 'warning':
        return {
          bgColor: 'bg-gradient-to-br from-amber-500 to-orange-500',
          textColor: 'text-white',
          message: 'Get Ready to Leave',
          icon: Clock,
          pulse: false
        };
      case 'missed':
        return {
          bgColor: 'bg-gradient-to-br from-gray-400 to-gray-500',
          textColor: 'text-white',
          message: 'Bus Missed',
          icon: RefreshCw,
          pulse: false
        };
      default:
        return {
          bgColor: 'bg-gradient-to-br from-teal-500 to-blue-500',
          textColor: 'text-white',
          message: 'Countdown Active',
          icon: Clock,
          pulse: false
        };
    }
  };

  const statusConfig = getStatusConfig();
  const StatusIcon = statusConfig.icon;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`${statusConfig.pulse ? 'animate-pulse' : ''}`}
    >
      <Card className="glass-effect border-0 shadow-2xl overflow-hidden">
        <CardContent className="p-0">
          <div className={`${statusConfig.bgColor} p-8 text-center`}>
            <StatusIcon className={`w-12 h-12 mx-auto mb-4 ${statusConfig.textColor} ${statusConfig.spin ? 'animate-spin' : ''}`} />
            <h2 className={`text-2xl font-bold mb-2 ${statusConfig.textColor}`}>
              {statusConfig.message}
            </h2>
            <div className={`text-5xl font-bold mb-4 ${statusConfig.textColor} font-mono`}>
              {formatTime(timeLeft)}
            </div>
            <p className={`${statusConfig.textColor} opacity-90`}>
              {status === 'loading' ? 'Calculating departure time...' :
               status === 'error' ? 'Unable to load countdown data' :
               status === 'missed' ? 'Try again with the next bus' : 'until departure'}
            </p>
          </div>

          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-slate-600" />
                <span className="text-sm font-medium text-slate-700">
                  {session.bus_stop?.stop_name || 'Bus Stop'}
                </span>
              </div>
              <Badge className="bg-teal-100 text-teal-700">
                Service {session.bus_service}
              </Badge>
            </div>

            {status === 'loading' ? (
              <div className="text-center space-y-2">
                <div className="h-4 bg-slate-200 rounded animate-pulse"></div>
                <div className="h-3 bg-slate-200 rounded animate-pulse w-3/4 mx-auto"></div>
              </div>
            ) : status === 'error' ? (
              <div className="text-center space-y-2">
                <p className="text-sm text-red-600">Unable to load countdown data</p>
                <p className="text-xs text-slate-500">Please try restarting the countdown</p>
              </div>
            ) : (
              <div className="text-center space-y-2">
                <p className="text-sm text-slate-600">
                  Bus arrives at {session.bus_arrival_time ?
                    new Date(session.bus_arrival_time).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : '--:--'}
                </p>
                <p className="text-xs text-slate-500">
                  Leave by {session.target_departure_time ?
                    new Date(session.target_departure_time).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : '--:--'}
                </p>
              </div>
            )}

            <div className="flex gap-3">
              <Button
                onClick={onStop}
                variant="outline"
                className="flex-1 rounded-xl"
                disabled={status === 'loading'}
              >
                <StopCircle className="w-4 h-4 mr-2" />
                Stop
              </Button>
              {(status === 'missed' || status === 'error') && (
                <Button
                  onClick={onRestart}
                  className="flex-1 btn-primary text-white rounded-xl"
                  disabled={status === 'loading'}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  {status === 'error' ? 'Retry' : 'Restart'}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}