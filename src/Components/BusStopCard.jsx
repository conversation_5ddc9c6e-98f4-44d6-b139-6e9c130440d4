
import React, { useState, useEffect } from "react";
import { Card, CardContent } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Badge } from "../components/ui/badge";
import { MapPin, Clock, PlayCircle, Timer, Loader2, AlertCircle, Accessibility, Users } from "lucide-react";
import { motion } from "framer-motion";

export default function BusStopCard({ busStop, onStartCountdown, isActive }) {
  const [arrivals, setArrivals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [startingCountdown, setStartingCountdown] = useState(null); // Track which arrival is starting countdown

  useEffect(() => {
    fetchBusArrivals();
    // Refresh arrivals every 30 seconds
    const interval = setInterval(fetchBusArrivals, 30000);
    return () => clearInterval(interval);
  }, [busStop.stop_code]);

  const fetchBusArrivals = async () => {
    try {
      setError(null);
      const busArrivals = await busStop.getBusArrivals();
      setArrivals(busArrivals);
    } catch (err) {
      console.error("Error fetching bus arrivals:", err);
      setError("Unable to load bus arrivals");
    } finally {
      setIsLoading(false);
    }
  };

  const getLoadIcon = (load) => {
    switch (load) {
      case 'SEA': return <Users className="w-3 h-3 text-green-600" title="Seats Available" />;
      case 'SDA': return <Users className="w-3 h-3 text-yellow-600" title="Standing Available" />;
      case 'LSD': return <Users className="w-3 h-3 text-red-600" title="Limited Standing" />;
      default: return null;
    }
  };

  const getBusTypeDisplay = (type) => {
    switch (type) {
      case 'SD': return 'Single Deck';
      case 'DD': return 'Double Deck';
      case 'BD': return 'Bendy';
      default: return type;
    }
  };

  const handleStartCountdown = async (arrival) => {
    const arrivalKey = `${arrival.service}-${arrival.arrivalTime}`;
    setStartingCountdown(arrivalKey);

    try {
      await onStartCountdown(busStop, arrival);
    } catch (error) {
      console.error("Error starting countdown:", error);
    } finally {
      setStartingCountdown(null);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Card className={`glass-effect border-0 shadow-xl overflow-hidden ${
        isActive ? 'ring-2 ring-teal-500' : ''
      }`}>
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start gap-3">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-teal-100 to-blue-100 flex items-center justify-center">
                <MapPin className="w-6 h-6 text-teal-600" />
              </div>
              <div>
                <h3 className="font-bold text-slate-800 text-lg">{busStop.stop_name}</h3>
                <p className="text-slate-600 text-sm">Stop {busStop.stop_code}</p>
                <div className="flex items-center gap-1 mt-1">
                  <Timer className="w-4 h-4 text-slate-400" />
                  <span className="text-xs text-slate-500">{busStop.walking_time_minutes} min walk</span>
                </div>
              </div>
            </div>
            {busStop.is_primary && (
              <Badge className="bg-gradient-to-r from-teal-500 to-blue-500 text-white">
                Primary
              </Badge>
            )}
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold text-slate-700 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Choose Your Bus
              {isLoading && <Loader2 className="w-4 h-4 animate-spin text-slate-400" />}
            </h4>

            {error ? (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-center">
                <AlertCircle className="w-4 h-4 text-red-600 mx-auto mb-1" />
                <p className="text-sm text-red-700">{error}</p>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={fetchBusArrivals}
                  className="mt-2 text-red-600 hover:text-red-700"
                >
                  Retry
                </Button>
              </div>
            ) : arrivals.length === 0 && !isLoading ? (
              <div className="p-3 bg-slate-50 border border-slate-200 rounded-lg text-center">
                <p className="text-sm text-slate-600">No bus arrivals available</p>
              </div>
            ) : (
              <div className="space-y-2">
                {arrivals.map((arrival, index) => (
                  <div key={`${arrival.service}-${index}`} className="flex items-center justify-between p-3 bg-white/60 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge
                        variant="outline"
                        className="font-bold text-teal-700 border-teal-200 bg-teal-50 w-14 justify-center"
                      >
                        {arrival.service}
                      </Badge>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-slate-700">{arrival.arrivalTime}</span>
                          {arrival.load && getLoadIcon(arrival.load)}
                          {arrival.feature === 'WAB' && (
                            <Accessibility className="w-3 h-3 text-blue-600" title="Wheelchair Accessible" />
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-slate-500">
                          {arrival.type === 'arriving' && (
                            <span className="text-green-600">Arriving Soon</span>
                          )}
                          {arrival.busType && (
                            <span>{getBusTypeDisplay(arrival.busType)}</span>
                          )}
                          {arrival.operator && (
                            <span>• {arrival.operator}</span>
                          )}
                          {!arrival.monitored && (
                            <span className="text-orange-600">• Scheduled</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => handleStartCountdown(arrival)}
                      disabled={isActive || startingCountdown === `${arrival.service}-${arrival.arrivalTime}`}
                      className="rounded-full text-teal-600 hover:bg-teal-100 hover:text-teal-700 disabled:opacity-50"
                    >
                      {startingCountdown === `${arrival.service}-${arrival.arrivalTime}` ? (
                        <Loader2 className="w-6 h-6 animate-spin" />
                      ) : (
                        <PlayCircle className="w-6 h-6" />
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {isActive && (
            <div className="mt-4 p-3 bg-teal-50 border border-teal-200 rounded-lg text-center text-teal-700 font-medium text-sm flex items-center justify-center gap-2">
              <Timer className="w-4 h-4" />
              Countdown is active for this stop
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
