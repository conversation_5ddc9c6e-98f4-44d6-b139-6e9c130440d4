// Simple in-memory storage for development
let sessionStorage = [];

export class CountdownSession {
  constructor(data = {}) {
    this.id = data.id;
    this.bus_stop = data.bus_stop;
    this.bus_service = data.bus_service;
    this.bus_arrival_time = data.bus_arrival_time; // When the bus actually arrives
    this.target_departure_time = data.target_departure_time; // When user should leave
    this.is_active = data.is_active !== undefined ? data.is_active : true;
    this.created_date = data.created_date || new Date();
    this.walking_buffer_minutes = data.walking_buffer_minutes || 3;
  }

  static async filter(filters = {}, orderBy = "-created_date") {
    // Filter sessions based on criteria
    let filtered = sessionStorage.filter(session => {
      if (filters.is_active !== undefined && session.is_active !== filters.is_active) {
        return false;
      }
      return true;
    });

    // Simple ordering by created_date
    if (orderBy === "-created_date") {
      filtered.sort((a, b) => new Date(b.created_date) - new Date(a.created_date));
    }

    return filtered;
  }

  static async create(data) {
    const session = new CountdownSession({
      id: Date.now(),
      ...data,
      created_date: new Date()
    });

    sessionStorage.push(session);
    return session;
  }

  async save() {
    const index = sessionStorage.findIndex(s => s.id === this.id);
    if (index !== -1) {
      sessionStorage[index] = this;
    }
    return this;
  }

  async delete() {
    const index = sessionStorage.findIndex(s => s.id === this.id);
    if (index !== -1) {
      sessionStorage.splice(index, 1);
    }
    return this;
  }

  getTimeRemaining() {
    if (!this.target_departure_time) return null;

    const now = new Date();
    const targetTime = new Date(this.target_departure_time);
    const diffMs = targetTime.getTime() - now.getTime();

    if (diffMs <= 0) return null;

    const minutes = Math.floor(diffMs / 60000);
    const seconds = Math.floor((diffMs % 60000) / 1000);

    return { minutes, seconds };
  }
}
