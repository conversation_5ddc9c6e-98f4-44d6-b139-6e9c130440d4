export class BusStop {
  constructor(data = {}) {
    this.id = data.id;
    this.stop_code = data.stop_code || data.BusStopCode;
    this.stop_name = data.stop_name || data.Description;
    this.road_name = data.road_name || data.RoadName;
    this.latitude = data.latitude || data.Latitude;
    this.longitude = data.longitude || data.Longitude;
    this.walking_time_minutes = data.walking_time_minutes || 5;
    this.is_primary = data.is_primary || false;
    this.distance_meters = data.distance_meters || null;
  }

  // Mock data for now - replace with actual API calls
  static async list(orderBy = "-created_date") {
    // Return mock bus stops with real stop codes for development
    return [
      new BusStop({
        id: 1,
        stop_code: "66609", // Real bus stop with active services
        stop_name: "Blk 203",
        road_name: "Toa Payoh North",
        walking_time_minutes: 3,
        is_primary: true,
        distance_meters: 200
      }),
      new BusStop({
        id: 2,
        stop_code: "66601", // Another real bus stop
        stop_name: "Blk 190",
        road_name: "Toa Payoh Central",
        walking_time_minutes: 5,
        is_primary: false,
        distance_meters: 350
      })
    ];
  }

  static async findByPostalCode(postalCode) {
    // In real implementation, this would:
    // 1. Convert postal code to coordinates
    // 2. Call SBS Transit API to find nearby bus stops
    // 3. Return bus stops with walking time calculations

    // For now, return mock data with real bus stop codes that have actual arrivals
    return [
      new BusStop({
        id: 1,
        stop_code: "66609", // Real bus stop with active services
        stop_name: "Blk 203",
        road_name: "Toa Payoh North",
        walking_time_minutes: 3,
        is_primary: true,
        distance_meters: 200
      }),
      new BusStop({
        id: 2,
        stop_code: "66601", // Another real bus stop
        stop_name: "Blk 190",
        road_name: "Toa Payoh Central",
        walking_time_minutes: 5,
        is_primary: false,
        distance_meters: 350
      }),
      new BusStop({
        id: 3,
        stop_code: "66611", // Another real bus stop
        stop_name: "Blk 230",
        road_name: "Toa Payoh North",
        walking_time_minutes: 7,
        is_primary: false,
        distance_meters: 480
      })
    ];
  }

  async getBusArrivals() {
    try {
      const response = await fetch(`https://arrivelah2.busrouter.sg/?id=${this.stop_code}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform API response to match our expected format
      const arrivals = [];

      if (data.services && Array.isArray(data.services)) {
        data.services.forEach(service => {
          // Add next arrival
          if (service.next && service.next.duration_ms) {
            const minutes = Math.ceil(service.next.duration_ms / (1000 * 60));
            arrivals.push({
              service: service.no,
              arrivalTime: minutes <= 1 ? "Arriving" : `${minutes} min`,
              type: minutes <= 3 ? "arriving" : "scheduled",
              load: service.next.load,
              feature: service.next.feature,
              busType: service.next.type,
              operator: service.operator,
              monitored: service.next.monitored === 1,
              rawData: service.next
            });
          }

          // Add subsequent arrival
          if (service.subsequent && service.subsequent.duration_ms) {
            const minutes = Math.ceil(service.subsequent.duration_ms / (1000 * 60));
            arrivals.push({
              service: service.no,
              arrivalTime: minutes <= 1 ? "Arriving" : `${minutes} min`,
              type: "scheduled",
              load: service.subsequent.load,
              feature: service.subsequent.feature,
              busType: service.subsequent.type,
              operator: service.operator,
              monitored: service.subsequent.monitored === 1,
              rawData: service.subsequent
            });
          }
        });
      }

      return arrivals;
    } catch (error) {
      console.error(`Error fetching bus arrivals for stop ${this.stop_code}:`, error);
      // Return empty array on error instead of mock data
      return [];
    }
  }
}
